<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['share']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['share']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6" data-share-id="<?php echo e($share->id); ?>">
    <!-- Share Header -->
    <div class="p-4 border-b border-gray-100">
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('profile.user', $share->user)); ?>">
                <img class="h-10 w-10 rounded-full" 
                     src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                     alt="<?php echo e($share->user->name); ?>">
            </a>
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <a href="<?php echo e(route('profile.user', $share->user)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                        <?php echo e($share->user->name); ?>

                    </a>
                    <span class="text-gray-500">shared a post</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <time datetime="<?php echo e($share->created_at->toISOString()); ?>">
                            <?php echo e($share->created_at->diffForHumans()); ?>

                        </time>
                        <span>•</span>

                        <!-- Privacy Scope Indicator -->
                        <?php
                            $scopeDetails = $share->getPrivacyScopeDetails();
                        ?>
                        <div class="flex items-center space-x-1" title="<?php echo e($scopeDetails['description']); ?>">
                            <?php if($share->privacy_scope === 'public'): ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            <?php elseif($share->privacy_scope === 'friends'): ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                            <?php elseif($share->privacy_scope === 'only_me'): ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            <?php endif; ?>
                            <span class="text-xs"><?php echo e($scopeDetails['label']); ?></span>
                        </div>
                    </div>

                    <!-- Share Actions Dropdown -->
                    <?php if(auth()->check() && (auth()->id() === $share->user_id || auth()->user()->isAdmin())): ?>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 top-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                <button onclick="editShareMessage(<?php echo e($share->id); ?>)"
                                        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit message</span>
                                </button>
                                <button onclick="deleteShare(<?php echo e($share->id); ?>)"
                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete share</span>
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Share Message (if any) -->
        <?php if($share->message): ?>
            <div class="mt-3 text-gray-700" id="share-message-<?php echo e($share->id); ?>">
                <p><?php echo nl2br(e($share->message)); ?></p>
            </div>
        <?php endif; ?>

        <!-- Edit Share Message Form (hidden by default) -->
        <div class="mt-3 hidden" id="edit-share-form-<?php echo e($share->id); ?>">
            <form class="edit-share-form" data-share-id="<?php echo e($share->id); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <!-- Privacy Scope Selector for Edit -->
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Privacy</label>
                    <div class="relative" x-data="{ open: false, selected: '<?php echo e($share->privacy_scope); ?>' }">
                        <button type="button" @click="open = !open"
                                class="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <div class="flex items-center space-x-2">
                                <svg x-show="selected === 'public'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <svg x-show="selected === 'friends'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                                <svg x-show="selected === 'only_me'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                <svg x-show="selected === 'custom'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span x-text="selected === 'public' ? 'Public' : selected === 'friends' ? 'Friends' : selected === 'only_me' ? 'Only me' : 'Custom'"></span>
                            </div>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5">
                            <div @click="selected = 'public'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-sm">Public</span>
                                </div>
                            </div>
                            <div @click="selected = 'friends'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                    <span class="text-sm">Friends</span>
                                </div>
                            </div>
                            <div @click="selected = 'only_me'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                    <span class="text-sm">Only me</span>
                                </div>
                            </div>
                            <div @click="selected = 'custom'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="text-sm">Custom</span>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="privacy_scope" :value="selected">
                    </div>
                </div>

                <textarea name="message" rows="3"
                          placeholder="Edit your message..."
                          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none"><?php echo e($share->message); ?></textarea>
                <div class="mt-2 flex justify-end space-x-2">
                    <button type="button" onclick="cancelEditShare(<?php echo e($share->id); ?>)"
                            class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                    <button type="submit"
                            class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Original Post Content (Embedded) -->
    <div class="mx-4 mb-4 border border-gray-200 rounded-lg overflow-hidden">
        <!-- Original Post Header -->
        <div class="p-4 bg-gray-50">
            <div class="flex items-center space-x-3">
                <?php if($share->post->group): ?>
                    <!-- Group Post Header (Facebook Style) -->
                    <div class="flex items-center space-x-2">
                        <!-- Group Avatar -->
                        <a href="<?php echo e(route('groups.show', $share->post->group)); ?>">
                            <img class="h-8 w-8 rounded-full"
                                 src="<?php echo e($share->post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->group->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                                 alt="<?php echo e($share->post->group->name); ?>">
                        </a>

                        <!-- User Avatar (smaller, positioned to overlap slightly) -->
                        <div class="relative -ml-1">
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                                <img class="h-6 w-6 rounded-full border-2 border-white hover:opacity-80 transition-opacity"
                                     src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                     alt="<?php echo e($share->post->user->name); ?>">
                            </a>
                        </div>
                    </div>

                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('groups.show', $share->post->group)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                                <?php echo e($share->post->group->name); ?>

                            </a>
                        </div>
                        <div class="flex items-center space-x-1 text-sm text-gray-500">
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="hover:text-custom-green font-medium">
                                <?php echo e($share->post->user->name); ?>

                            </a>
                            <span>•</span>
                            <span><?php echo e($share->post->published_at->diffForHumans()); ?></span>
                            <?php if($share->post->group->visibility === 'private'): ?>
                                <span>•</span>
                                <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd" />
                                </svg>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php elseif($share->post->organization): ?>
                    <!-- Organization Post Header -->
                    <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>">
                        <img class="h-8 w-8 rounded-full"
                             src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                             alt="<?php echo e($share->post->organization->name); ?>">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                                <?php echo e($share->post->organization->name); ?>

                            </a>
                            <span class="text-gray-500 text-sm">•</span>
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="text-sm text-gray-600 hover:text-custom-green">
                                by <?php echo e($share->post->user->name); ?>

                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($share->post->published_at->diffForHumans()); ?>

                        </div>
                    </div>
                <?php else: ?>
                    <!-- Regular User Post Header -->
                    <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                        <img class="h-8 w-8 rounded-full"
                             src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($share->post->user->name); ?>">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                                <?php echo e($share->post->user->name); ?>

                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($share->post->published_at->diffForHumans()); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Original Post Content -->
        <div class="p-4 bg-white">
            <a href="<?php echo e(route('posts.show', $share->post)); ?>" class="block hover:bg-gray-50 transition-colors rounded p-2 -m-2">
                <div class="flex items-start justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex-1"><?php echo e($share->post->title); ?></h3>
                    <svg class="w-5 h-5 text-gray-400 ml-2 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </div>
                
                <?php if($share->post->content): ?>
                    <div class="text-gray-700 mb-3">
                        <p><?php echo e(Str::limit($share->post->content, 200)); ?></p>
                    </div>
                <?php endif; ?>

                <!-- Post Images -->
                <?php if($share->post->images && count($share->post->images) > 0): ?>
                    <div class="mb-3">
                        <?php if(count($share->post->images) == 1): ?>
                            <div class="rounded-lg overflow-hidden">
                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0])); ?>" 
                                     alt="Post image" 
                                     class="w-full h-64 object-cover">
                            </div>
                        <?php else: ?>
                            <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                <?php $__currentLoopData = array_slice($share->post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="relative <?php echo e($index >= 2 ? 'hidden sm:block' : ''); ?>">
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                             alt="Post image <?php echo e($index + 1); ?>" 
                                             class="w-full h-32 object-cover">
                                        <?php if($index == 3 && count($share->post->images) > 4): ?>
                                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                <span class="text-white font-semibold">+<?php echo e(count($share->post->images) - 4); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Post Type Badge -->
                <?php if($share->post->type !== 'general'): ?>
                    <div class="mb-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php if($share->post->type === 'announcement'): ?> bg-blue-100 text-blue-800
                            <?php elseif($share->post->type === 'event'): ?> bg-green-100 text-green-800
                            <?php elseif($share->post->type === 'job'): ?> bg-purple-100 text-purple-800
                            <?php elseif($share->post->type === 'scholarship'): ?> bg-yellow-100 text-yellow-800
                            <?php else: ?> bg-gray-100 text-gray-800
                            <?php endif; ?>">
                            <?php echo e(ucfirst($share->post->type)); ?>

                        </span>
                    </div>
                <?php endif; ?>
            </a>
        </div>
    </div>

    <!-- Interaction Stats -->
    <div class="px-4 py-2 border-t border-gray-100">
        <div class="flex items-center justify-between text-sm text-gray-500">
            <div class="flex items-center space-x-4">
                <?php if($share->post->likes->count() > 0): ?>
                    <span class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                        </svg>
                        <span><?php echo e($share->post->likes->count()); ?></span>
                    </span>
                <?php endif; ?>
                
                <?php if($share->post->comments->count() > 0): ?>
                    <span><?php echo e($share->post->comments->count()); ?> comments</span>
                <?php endif; ?>
                
                <?php if($share->post->shares->count() > 0): ?>
                    <span><?php echo e($share->post->shares->count()); ?> shares</span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Action Buttons (for the SHARE, not the original post) -->
    <div class="px-4 py-3 border-t border-gray-100">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <!-- Like Button (likes the SHARE) -->
                <?php if(auth()->guard()->check()): ?>
                    <button onclick="toggleShareLike(<?php echo e($share->id); ?>)"
                            class="flex items-center space-x-2 text-gray-500 hover:text-red-600 transition-colors"
                            id="share-like-btn-<?php echo e($share->id); ?>">
                        <?php
                            $isLiked = $share->isLikedBy(auth()->user());
                        ?>
                        <svg class="w-5 h-5 <?php echo e($isLiked ? 'text-red-600 fill-current' : ''); ?>"
                             fill="<?php echo e($isLiked ? 'currentColor' : 'none'); ?>"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span class="text-sm" id="share-like-count-<?php echo e($share->id); ?>"><?php echo e($share->likes->count()); ?> likes</span>
                    </button>
                <?php else: ?>
                    <span class="flex items-center space-x-2 text-gray-400">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span class="text-sm"><?php echo e($share->likes->count()); ?> likes</span>
                    </span>
                <?php endif; ?>

                <!-- Comment Button (comments on the SHARE) -->
                <button onclick="toggleShareComments(<?php echo e($share->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm" id="share-comments-count-<?php echo e($share->id); ?>"><?php echo e($share->comments->count()); ?> comments</span>
                </button>

                <!-- Share Button (share the original post) -->
                <button onclick="openShareModal(<?php echo e($share->post->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="text-sm" id="shares-count-<?php echo e($share->post->id); ?>"><?php echo e($share->post->shares->count()); ?> shares</span>
                </button>

                <!-- View Original Post Button -->
                <button onclick="viewOriginalPost(<?php echo e($share->post->id); ?>)"
                        class="flex items-center space-x-2 text-gray-500 hover:text-purple-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    <span class="text-sm">View Original</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Inline Comments Section for SHARE (hidden by default) -->
    <div id="share-comments-section-<?php echo e($share->id); ?>" class="hidden border-t border-gray-200">
        <?php if (isset($component)) { $__componentOriginale2adaab6a901bf856821fcceeedc4714 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale2adaab6a901bf856821fcceeedc4714 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-comment-section','data' => ['share' => $share,'showInline' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-comment-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share),'showInline' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale2adaab6a901bf856821fcceeedc4714)): ?>
<?php $attributes = $__attributesOriginale2adaab6a901bf856821fcceeedc4714; ?>
<?php unset($__attributesOriginale2adaab6a901bf856821fcceeedc4714); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale2adaab6a901bf856821fcceeedc4714)): ?>
<?php $component = $__componentOriginale2adaab6a901bf856821fcceeedc4714; ?>
<?php unset($__componentOriginale2adaab6a901bf856821fcceeedc4714); ?>
<?php endif; ?>
    </div>

    <!-- Share Modal -->
    <?php if (isset($component)) { $__componentOriginal0dc3624784f5ac950932d2feff9a6435 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0dc3624784f5ac950932d2feff9a6435 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-modal','data' => ['post' => $share->post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share->post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $attributes = $__attributesOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $component = $__componentOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__componentOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/shared-post-card.blade.php ENDPATH**/ ?>