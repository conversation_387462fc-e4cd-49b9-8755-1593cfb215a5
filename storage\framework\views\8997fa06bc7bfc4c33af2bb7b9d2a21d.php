<?php $__env->startSection('title', 'Scholarships Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="admin-card p-8 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-custom-darkest to-custom-dark-gray bg-clip-text text-transparent">
                    Scholarships Management
                </h1>
                <p class="text-gray-600 mt-2 text-lg">Monitor and manage all scholarship listings in the system</p>
                <div class="flex items-center mt-3 text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    Financial aid and scholarship opportunities
                </div>
            </div>
            <div class="flex flex-col items-end space-y-3">
                <div class="custom-green-gradient text-white px-6 py-3 rounded-xl shadow-lg">
                    <p class="text-sm font-medium opacity-90">Total Scholarships</p>
                    <p class="text-2xl font-bold"><?php echo e(number_format($scholarship_stats['total'])); ?></p>
                </div>
                <a href="<?php echo e(route('scholarships.create')); ?>" class="action-btn custom-dark-gradient text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add New Scholarship
                </a>
            </div>
        </div>
    </div>

    <!-- Scholarship Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="admin-card p-6 border-l-4 border-custom-green">
            <div class="text-center">
                <div class="w-12 h-12 custom-green-gradient rounded-xl mx-auto mb-3 flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <p class="text-3xl font-bold text-custom-green"><?php echo e(number_format($scholarship_stats['total'])); ?></p>
                <p class="text-sm text-gray-600 font-medium">Total Scholarships</p>
            </div>
        </div>
        <div class="admin-card p-6 border-l-4 border-green-500">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl mx-auto mb-3 flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <p class="text-3xl font-bold text-green-600"><?php echo e(number_format($scholarship_stats['active'])); ?></p>
                <p class="text-sm text-gray-600 font-medium">Active</p>
            </div>
        </div>
        <div class="admin-card p-6 border-l-4 border-gray-500">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-gray-500 to-gray-600 rounded-xl mx-auto mb-3 flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                    </svg>
                </div>
                <p class="text-3xl font-bold text-gray-600"><?php echo e(number_format($scholarship_stats['inactive'])); ?></p>
                <p class="text-sm text-gray-600 font-medium">Inactive</p>
            </div>
        </div>
        <div class="admin-card p-6 border-l-4 border-red-500">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-xl mx-auto mb-3 flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <p class="text-3xl font-bold text-red-600"><?php echo e(number_format($scholarship_stats['expired'])); ?></p>
                <p class="text-sm text-gray-600 font-medium">Expired</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="GET" action="<?php echo e(route('admin.scholarships.index')); ?>" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Title or provider..." 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Scholarships Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Scholarships (<?php echo e($scholarships->total()); ?>)</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scholarship</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deadline</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $scholarships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scholarship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="max-w-xs">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($scholarship->title); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e(Str::limit($scholarship->description, 100)); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($scholarship->provider); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php if($scholarship->amount): ?>
                                        $<?php echo e(number_format($scholarship->amount, 2)); ?>

                                    <?php else: ?>
                                        <span class="text-gray-500">Not specified</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php echo e($scholarship->deadline ? $scholarship->deadline->format('M d, Y') : 'No deadline'); ?>

                                </div>
                                <?php if($scholarship->deadline && $scholarship->deadline->isPast()): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Expired
                                    </span>
                                <?php elseif($scholarship->deadline && $scholarship->deadline->diffInDays() <= 7): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Expiring Soon
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    <?php if($scholarship->status === 'active'): ?> bg-green-100 text-green-800
                                    <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                    <?php echo e(ucfirst($scholarship->status)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-700"><?php echo e(substr($scholarship->creator->name, 0, 1)); ?></span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($scholarship->creator->name); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($scholarship->created_at->format('M d, Y')); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex flex-col space-y-1">
                                    <a href="<?php echo e(route('scholarships.show', $scholarship)); ?>" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    <a href="<?php echo e(route('scholarships.edit', $scholarship)); ?>" class="text-yellow-600 hover:text-yellow-900">Edit</a>
                                    <?php if($scholarship->status === 'active'): ?>
                                        <button class="text-red-600 hover:text-red-900 text-left">Deactivate</button>
                                    <?php else: ?>
                                        <button class="text-green-600 hover:text-green-900 text-left">Activate</button>
                                    <?php endif; ?>
                                    <button class="text-red-600 hover:text-red-900 text-left">Delete</button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No scholarships found matching your criteria.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($scholarships->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($scholarships->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>

    <!-- Quick Stats -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Insights</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <p class="text-2xl font-bold text-blue-600">
                    $<?php echo e(number_format($scholarships->where('status', 'active')->sum('amount'), 2)); ?>

                </p>
                <p class="text-sm text-blue-800">Total Active Scholarship Value</p>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <p class="text-2xl font-bold text-green-600">
                    <?php echo e($scholarships->where('deadline', '>=', now())->count()); ?>

                </p>
                <p class="text-sm text-green-800">Available Scholarships</p>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <p class="text-2xl font-bold text-yellow-600">
                    <?php echo e($scholarships->where('deadline', '<=', now()->addDays(30))->where('deadline', '>=', now())->count()); ?>

                </p>
                <p class="text-sm text-yellow-800">Expiring in 30 Days</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/admin/scholarships/index.blade.php ENDPATH**/ ?>